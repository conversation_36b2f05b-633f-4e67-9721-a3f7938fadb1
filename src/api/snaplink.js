import apiClient from './client';

// Collection endpoints
export const createCollection = async (collectionData) => {
  const { data } = await apiClient.post('/sl/v1/collection/create', collectionData);
  return data;
};

export const deleteCollection = async (collection_id ) => {
  const { data } = await apiClient.post('/sl/v1/collection/delete', { collection_id  });
  return data;
};

export const publishCollection = async (collection_id ) => {
  const { data } = await apiClient.post('/sl/v1/collection/publish', { collection_id  });
  return data;
};

export const addCollectionResource = async (resourceData) => {
  const { data } = await apiClient.post('/sl/v1/collection/resource/add', resourceData);
  return data;
};

// Resource endpoints
export const reportResourceStatus = async (statusData) => {
  const { data } = await apiClient.post('/sl/v1/resource/status/report', statusData);
  return data;
};

export const getUserResources = async ({ page = 0, size = 10 } = {}) => {
  const { data } = await apiClient.post('/sl/v1/my/collections', {
    paginator: { page, size }
  }, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return data;
};

// Upload endpoints
export const getUploadResourceURL = async (resourceData) => {
  const { data } = await apiClient.post('/sl/v1/upload/resource/url', resourceData);
  return data;
};

// Multipart upload endpoints
export const initiateMultipartUpload = async (uploadData) => {
  const { data } = await apiClient.post('/sl/v1/multipart/upload', uploadData);
  return data;
};

export const getUploadPartUrl = async (partData) => {
  const { data } = await apiClient.post('/sl/v1/upload/part/url', partData);
  return data;
};

export const completeMultipartUpload = async (completeData) => {
  const { data } = await apiClient.post('/sl/v1/complete/upload', completeData);
  return data;
};

// Banner upload endpoint
export const getBannerUploadURL = async (bannerData) => {
  const { data } = await apiClient.post('/sl/v1/create/banner/url', bannerData);
  return data;
};

// User endpoints
export const buyCollection = async (params) => {
  const { data } = await apiClient.post('/sl/v1/my/collections', params);
  return data;
};

export const getMyOrders = async (paginator = { page: 0, size: 10 }) => {
  const { data } = await apiClient.post('/sl/v1/my/orders', { paginator }, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return data;
};


export const getCollection = async (reqData) => {
  const { data } = await apiClient.post('/sl/v1/collection/get', reqData, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return data;
};

export const listCollections = async (reqData) => {
  const { data } = await apiClient.post('/sl/v1/collection/list', reqData, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
  return data;
};
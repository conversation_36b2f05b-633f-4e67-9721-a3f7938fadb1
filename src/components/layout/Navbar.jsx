import { useState, useEffect } from 'react';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { Disclosure, Menu, Transition } from '@headlessui/react';
import { Bars3Icon, XMarkIcon, UserCircleIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

import useAuth from '@hooks/useAuth';
import { getAppName } from '@utils/config';

const navigation = [
  { name: 'Home', to: '/' },
];

const Navbar = () => {
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();
  const [scrolled, setScrolled] = useState(false);

  // Dynamic navigation for authenticated users
  const navLinks = isAuthenticated
    ? [
      { name: 'Home', to: '/' },
      { name: 'My Selling', to: '/profile/selling' },
      { name: 'My Buy', to: '/profile/buy' },
    ]
    : [
      { name: 'Home', to: '/' },
    ];

  // Handle scroll events to change navbar appearance
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <Disclosure
      as="nav"
      className={clsx(
        'sticky top-0 z-50 transition-all duration-300',
        scrolled ? 'glass-dark py-2' : 'py-4'
      )}
    >
      {({ open }) => (
        <>
          <div className="mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Link to="/" className="flex items-center">
                    <span className="text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                      {getAppName()}
                    </span>
                  </Link>
                </div>
                <div className="hidden md:ml-8 md:flex md:space-x-8">
                  {navLinks.map((item) => (
                    <NavLink
                      key={item.name}
                      to={item.to}
                      className={({ isActive }) =>
                        clsx(
                          'px-3 py-2 text-sm font-medium transition-colors',
                          isActive
                            ? 'text-white border-b-2 border-primary-500'
                            : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-primary-500/70'
                        )
                      }
                      end
                    >
                      {item.name}
                    </NavLink>
                  ))}
                </div>
              </div>

              <div className="hidden md:block">
                <div className="flex items-center space-x-4">
                  {isAuthenticated ? (
                    <Menu as="div" className="relative ml-3">
                      <div>
                        <Menu.Button className="relative flex rounded-full text-sm focus:outline-none">
                          <span className="sr-only">Open user menu</span>
                          {user?.avatar ? (
                            <img
                              className="h-9 w-9 rounded-full object-cover"
                              src={user.avatar}
                              alt={user.username}
                            />
                          ) : (
                            <div className="h-9 w-9 rounded-full bg-primary-600 flex items-center justify-center text-white font-medium">
                              {user?.username?.substring(0, 1).toUpperCase() || (
                                <UserCircleIcon className="h-7 w-7" />
                              )}
                            </div>
                          )}
                        </Menu.Button>
                      </div>
                      <Transition
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right glass-dark rounded-lg py-1 shadow-glass-strong">
                          <div className="px-4 py-2 border-b border-white/5">
                            <p className="text-sm text-white">{user?.username}</p>
                            <p className="text-xs text-gray-400 truncate">{user?.email}</p>
                          </div>
                          <Menu.Item>
                            {({ active }) => (
                              <Link
                                to="/profile"
                                className={clsx(
                                  active ? 'bg-white/10' : '',
                                  'block px-4 py-2 text-sm text-white'
                                )}
                              >
                                Your Profile
                              </Link>
                            )}
                          </Menu.Item>
                          <Menu.Item>
                            {({ active }) => (
                              <Link
                                to="/upload"
                                className={clsx(
                                  active ? 'bg-white/10' : '',
                                  'block px-4 py-2 text-sm text-white'
                                )}
                              >
                                Create Collection
                              </Link>
                            )}
                          </Menu.Item>

                          <Menu.Item>
                            {({ active }) => (
                              <Link
                                to="/transactions"
                                className={clsx(
                                  active ? 'bg-white/10' : '',
                                  'block px-4 py-2 text-sm text-white'
                                )}
                              >
                                Transactions
                              </Link>
                            )}
                          </Menu.Item>
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                onClick={logout}
                                className={clsx(
                                  active ? 'bg-white/10' : '',
                                  'block w-full text-left px-4 py-2 text-sm text-white'
                                )}
                              >
                                Sign out
                              </button>
                            )}
                          </Menu.Item>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  ) : (
                    <>
                      <Link to="/login" className="btn-glass text-sm">
                        Log in
                      </Link>
                      <Link to="/register" className="btn-primary text-sm">
                        Sign up
                      </Link>
                    </>
                  )}
                  {isAuthenticated && (
                    <Link
                      to="/upload"
                      className="btn-primary text-sm flex items-center space-x-2"
                    >
                      <span>Create</span>
                    </Link>
                  )}
                </div>
              </div>

              <div className="flex md:hidden">
                <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-300 hover:text-white focus:outline-none">
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="md:hidden glass-dark border-t border-white/5">
            <div className="space-y-1 px-2 pb-3 pt-2">
              {navLinks.map((item) => (
                <NavLink
                  key={item.name}
                  to={item.to}
                  className={({ isActive }) =>
                    clsx(
                      'block rounded-md px-3 py-2 text-base font-medium',
                      isActive
                        ? 'bg-primary-800/50 text-white'
                        : 'text-gray-300 hover:bg-primary-800/30 hover:text-white'
                    )
                  }
                  end
                >
                  {item.name}
                </NavLink>
              ))}
            </div>
            <div className="border-t border-white/10 pb-3 pt-4">
              {isAuthenticated ? (
                <>
                  <div className="flex items-center px-5">
                    <div className="flex-shrink-0">
                      {user?.avatar ? (
                        <img className="h-10 w-10 rounded-full" src={user.avatar} alt="" />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center text-white font-medium">
                          {user?.username?.substring(0, 1).toUpperCase() || (
                            <UserCircleIcon className="h-7 w-7" />
                          )}
                        </div>
                      )}
                    </div>
                    <div className="ml-3">
                      <div className="text-base font-medium text-white">{user?.username}</div>
                      <div className="text-sm text-gray-400">{user?.email}</div>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1 px-2">
                    <Disclosure.Button
                      as={Link}
                      to="/profile"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                    >
                      Your Profile
                    </Disclosure.Button>
                    <Disclosure.Button
                      as={Link}
                      to="/upload"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                    >
                      Create Collection
                    </Disclosure.Button>

                    <Disclosure.Button
                      as={Link}
                      to="/transactions"
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                    >
                      Transactions
                    </Disclosure.Button>
                    <Disclosure.Button
                      as="button"
                      onClick={logout}
                      className="block w-full text-left rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                    >
                      Sign out
                    </Disclosure.Button>
                  </div>
                </>
              ) : (
                <div className="mt-3 space-y-1 px-2">
                  <Disclosure.Button
                    as={Link}
                    to="/login"
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                  >
                    Log in
                  </Disclosure.Button>
                  <Disclosure.Button
                    as={Link}
                    to="/register"
                    className="block rounded-md px-3 py-2 text-base font-medium text-gray-300 hover:bg-primary-800/30 hover:text-white"
                  >
                    Sign up
                  </Disclosure.Button>
                </div>
              )}
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
};

export default Navbar;
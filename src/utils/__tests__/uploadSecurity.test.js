/**
 * Security tests for upload functionality
 * Tests MD5 calculation security, memory usage, and error handling
 */

import { calculateMD5 } from '../md5';
import { 
  validateFileForUpload, 
  getMemoryUsage, 
  hasSufficientMemory,
  getRecommendedChunkSize,
  uploadSecurityLogger 
} from '../uploadSecurity';

// Mock File and Blob for testing
class MockFile extends File {
  constructor(name, size, type, content = 'test content') {
    // Create a proper File-like object
    const blob = new Blob([content], { type });
    super([blob], name, { type, lastModified: Date.now() });
    
    // Override size for testing
    Object.defineProperty(this, 'size', {
      value: size,
      writable: false
    });
  }
}

class MockBlob extends Blob {
  constructor(size, content = 'x'.repeat(size)) {
    super([content]);
    Object.defineProperty(this, 'size', {
      value: size,
      writable: false
    });
  }
}

describe('Upload Security Tests', () => {
  
  describe('MD5 Security Enhancements', () => {
    test('should reject files exceeding size limit', async () => {
      const largeFile = new MockFile('large.txt', 100 * 1024 * 1024 + 1, 'text/plain'); // 100MB + 1 byte
      
      await expect(calculateMD5(largeFile, { maxSize: 100 * 1024 * 1024 }))
        .rejects.toThrow('exceeds maximum allowed size');
    });

    test('should reject empty files', async () => {
      const emptyFile = new MockFile('empty.txt', 0, 'text/plain', '');
      
      await expect(calculateMD5(emptyFile))
        .rejects.toThrow('Cannot calculate MD5 for empty file');
    });

    test('should reject invalid file objects', async () => {
      await expect(calculateMD5(null))
        .rejects.toThrow('Invalid file or blob object');
      
      await expect(calculateMD5({}))
        .rejects.toThrow('Invalid file or blob object');
    });

    test('should handle retry mechanism', async () => {
      // Mock FileReader to fail first attempt
      const originalFileReader = global.FileReader;
      let attemptCount = 0;
      
      global.FileReader = class MockFileReader {
        readAsArrayBuffer() {
          attemptCount++;
          setTimeout(() => {
            if (attemptCount === 1) {
              this.onerror(new Error('Simulated failure'));
            } else {
              // Simulate successful read on second attempt
              this.onload({ target: { result: new ArrayBuffer(8) } });
            }
          }, 10);
        }
      };

      const testFile = new MockFile('test.txt', 100, 'text/plain');
      
      // Should succeed after retry
      await expect(calculateMD5(testFile, { retries: 2 }))
        .resolves.toBeDefined();
      
      expect(attemptCount).toBe(2);
      
      // Restore original FileReader
      global.FileReader = originalFileReader;
    });

    test('should validate MD5 format options', async () => {
      const testFile = new MockFile('test.txt', 100, 'text/plain');
      
      await expect(calculateMD5(testFile, { format: 'invalid' }))
        .rejects.toThrow('Unsupported format');
    });
  });

  describe('File Validation Security', () => {
    test('should validate file size limits', () => {
      const largeFile = new MockFile('large.mp4', 200 * 1024 * 1024, 'video/mp4'); // 200MB
      
      const result = validateFileForUpload(largeFile, { maxSize: 100 * 1024 * 1024 });
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('exceeds maximum allowed size'));
    });

    test('should validate file types', () => {
      const textFile = new MockFile('document.txt', 1024, 'text/plain');
      
      const result = validateFileForUpload(textFile, { allowedTypes: ['image/*', 'video/*'] });
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('is not allowed'));
    });

    test('should detect malicious file names', () => {
      const maliciousFile = new MockFile('../../../etc/passwd', 1024, 'text/plain');
      
      const result = validateFileForUpload(maliciousFile);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('invalid characters'));
    });

    test('should validate file name length', () => {
      const longName = 'a'.repeat(300) + '.txt';
      const longNameFile = new MockFile(longName, 1024, 'text/plain');
      
      const result = validateFileForUpload(longNameFile, { maxNameLength: 255 });
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain(expect.stringContaining('File name too long'));
    });

    test('should detect empty files', () => {
      const emptyFile = new MockFile('empty.txt', 0, 'text/plain');
      
      const result = validateFileForUpload(emptyFile);
      
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('File is empty');
    });

    test('should provide warnings for large files', () => {
      const largeFile = new MockFile('large.mp4', 60 * 1024 * 1024, 'video/mp4'); // 60MB
      
      const result = validateFileForUpload(largeFile);
      
      expect(result.valid).toBe(true);
      expect(result.warnings).toContain(expect.stringContaining('Large file detected'));
    });
  });

  describe('Memory Usage Monitoring', () => {
    test('should return memory usage information when available', () => {
      // Mock performance.memory
      const originalMemory = performance.memory;
      performance.memory = {
        usedJSHeapSize: 50 * 1024 * 1024,
        totalJSHeapSize: 100 * 1024 * 1024,
        jsHeapSizeLimit: 200 * 1024 * 1024,
      };

      const memInfo = getMemoryUsage();
      
      expect(memInfo).toBeDefined();
      expect(memInfo.usagePercentage).toBe(25); // 50MB / 200MB * 100
      expect(memInfo.available).toBe(150 * 1024 * 1024); // 200MB - 50MB

      // Restore original
      performance.memory = originalMemory;
    });

    test('should handle missing memory API gracefully', () => {
      const originalMemory = performance.memory;
      delete performance.memory;

      const memInfo = getMemoryUsage();
      expect(memInfo).toBeNull();

      const hasMem = hasSufficientMemory(1024 * 1024);
      expect(hasMem).toBe(true); // Should assume sufficient when can't measure

      // Restore original
      performance.memory = originalMemory;
    });

    test('should calculate recommended chunk size based on memory', () => {
      const originalMemory = performance.memory;
      performance.memory = {
        usedJSHeapSize: 50 * 1024 * 1024,
        totalJSHeapSize: 100 * 1024 * 1024,
        jsHeapSizeLimit: 200 * 1024 * 1024,
      };

      const fileSize = 50 * 1024 * 1024; // 50MB
      const defaultChunkSize = 5 * 1024 * 1024; // 5MB
      
      const recommendedSize = getRecommendedChunkSize(fileSize, defaultChunkSize);
      
      expect(recommendedSize).toBeGreaterThan(0);
      expect(recommendedSize).toBeLessThanOrEqual(10 * 1024 * 1024); // Max 10MB
      expect(recommendedSize).toBeGreaterThanOrEqual(1 * 1024 * 1024); // Min 1MB

      // Restore original
      performance.memory = originalMemory;
    });
  });

  describe('Security Event Logging', () => {
    beforeEach(() => {
      // Clear previous events
      uploadSecurityLogger.events = [];
    });

    test('should log security events', () => {
      uploadSecurityLogger.logEvent('test', 'Test security event', { testData: 'value' });
      
      const events = uploadSecurityLogger.getRecentEvents(1);
      expect(events).toHaveLength(1);
      expect(events[0].type).toBe('test');
      expect(events[0].message).toBe('Test security event');
      expect(events[0].metadata.testData).toBe('value');
    });

    test('should filter events by type', () => {
      uploadSecurityLogger.logEvent('error', 'Error event');
      uploadSecurityLogger.logEvent('warning', 'Warning event');
      uploadSecurityLogger.logEvent('error', 'Another error event');
      
      const errorEvents = uploadSecurityLogger.getEventsByType('error');
      expect(errorEvents).toHaveLength(2);
      
      const warningEvents = uploadSecurityLogger.getEventsByType('warning');
      expect(warningEvents).toHaveLength(1);
    });

    test('should limit stored events', () => {
      const originalMaxEvents = uploadSecurityLogger.maxEvents;
      uploadSecurityLogger.maxEvents = 5;

      // Log more events than the limit
      for (let i = 0; i < 10; i++) {
        uploadSecurityLogger.logEvent('test', `Event ${i}`);
      }

      expect(uploadSecurityLogger.events).toHaveLength(5);
      expect(uploadSecurityLogger.events[0].message).toBe('Event 5'); // Should keep last 5

      // Restore original
      uploadSecurityLogger.maxEvents = originalMaxEvents;
    });
  });

  describe('Integration Security Tests', () => {
    test('should handle complete secure upload flow', async () => {
      const testFile = new MockFile('test.mp4', 2 * 1024 * 1024, 'video/mp4'); // 2MB
      
      // 1. Validate file
      const validation = validateFileForUpload(testFile);
      expect(validation.valid).toBe(true);
      
      // 2. Check memory
      const hasMemory = hasSufficientMemory(testFile.size);
      expect(hasMemory).toBe(true);
      
      // 3. Calculate MD5 with security options
      const md5 = await calculateMD5(testFile, {
        maxSize: 10 * 1024 * 1024,
        retries: 2
      });
      
      expect(md5).toBeDefined();
      expect(typeof md5).toBe('string');
      expect(md5.length).toBeGreaterThan(0);
    });

    test('should reject suspicious upload attempts', async () => {
      // Test various attack scenarios
      const attackScenarios = [
        {
          name: 'Oversized file',
          file: new MockFile('huge.mp4', 1000 * 1024 * 1024, 'video/mp4'), // 1GB
          expectedError: 'exceeds maximum allowed size'
        },
        {
          name: 'Path traversal filename',
          file: new MockFile('../../malicious.exe', 1024, 'application/octet-stream'),
          expectedError: 'invalid characters'
        },
        {
          name: 'Empty file',
          file: new MockFile('empty.txt', 0, 'text/plain'),
          expectedError: 'File is empty'
        }
      ];

      for (const scenario of attackScenarios) {
        const validation = validateFileForUpload(scenario.file);
        expect(validation.valid).toBe(false);
        expect(validation.errors.some(error => 
          error.includes(scenario.expectedError)
        )).toBe(true);
      }
    });
  });
});

// Console output for manual verification
console.log('=== Upload Security Test Summary ===');
console.log('✅ MD5 calculation security enhancements tested');
console.log('✅ File validation security checks tested');
console.log('✅ Memory usage monitoring tested');
console.log('✅ Security event logging tested');
console.log('✅ Integration security flow tested');
console.log('✅ Attack scenario prevention tested');

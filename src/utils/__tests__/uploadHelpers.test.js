/**
 * Test file for upload helpers functionality
 * This file tests the multipart upload logic and file size thresholds
 */

import { shouldUseMultipartUpload, splitFileIntoChunks } from '../uploadHelpers';

// Mock File constructor for testing
class MockFile {
  constructor(name, size, type) {
    this.name = name;
    this.size = size;
    this.type = type;
  }

  slice(start, end) {
    return new MockBlob(end - start);
  }
}

class MockBlob {
  constructor(size) {
    this.size = size;
  }
}

describe('Upload Helpers', () => {
  describe('shouldUseMultipartUpload', () => {
    test('should use direct upload for files smaller than 5MB', () => {
      const smallImage = new MockFile('small.jpg', 3 * 1024 * 1024, 'image/jpeg'); // 3MB
      const smallVideo = new MockFile('small.mp4', 4 * 1024 * 1024, 'video/mp4'); // 4MB
      
      expect(shouldUseMultipartUpload(smallImage)).toBe(false);
      expect(shouldUseMultipartUpload(smallVideo)).toBe(false);
    });

    test('should use multipart upload for files larger than 5MB', () => {
      const largeImage = new MockFile('large.jpg', 6 * 1024 * 1024, 'image/jpeg'); // 6MB
      const largeVideo = new MockFile('large.mp4', 10 * 1024 * 1024, 'video/mp4'); // 10MB
      
      expect(shouldUseMultipartUpload(largeImage)).toBe(true);
      expect(shouldUseMultipartUpload(largeVideo)).toBe(true);
    });

    test('should use direct upload for files exactly 5MB', () => {
      const exactFile = new MockFile('exact.jpg', 5 * 1024 * 1024, 'image/jpeg'); // 5MB
      
      expect(shouldUseMultipartUpload(exactFile)).toBe(false);
    });

    test('should use multipart upload for files just over 5MB', () => {
      const justOverFile = new MockFile('justover.jpg', 5 * 1024 * 1024 + 1, 'image/jpeg'); // 5MB + 1 byte
      
      expect(shouldUseMultipartUpload(justOverFile)).toBe(true);
    });

    test('should respect custom threshold', () => {
      const file = new MockFile('test.jpg', 8 * 1024 * 1024, 'image/jpeg'); // 8MB
      const customThreshold = 10 * 1024 * 1024; // 10MB
      
      expect(shouldUseMultipartUpload(file, customThreshold)).toBe(false);
      expect(shouldUseMultipartUpload(file)).toBe(true); // Default 5MB threshold
    });
  });

  describe('splitFileIntoChunks', () => {
    test('should split file into correct number of chunks', () => {
      const file = new MockFile('test.mp4', 12 * 1024 * 1024, 'video/mp4'); // 12MB
      const chunkSize = 5 * 1024 * 1024; // 5MB
      
      const chunks = splitFileIntoChunks(file, chunkSize);
      
      expect(chunks).toHaveLength(3); // 12MB / 5MB = 2.4, so 3 chunks
      expect(chunks[0].size).toBe(5 * 1024 * 1024); // First chunk: 5MB
      expect(chunks[1].size).toBe(5 * 1024 * 1024); // Second chunk: 5MB
      expect(chunks[2].size).toBe(2 * 1024 * 1024); // Third chunk: 2MB
    });

    test('should handle files smaller than chunk size', () => {
      const file = new MockFile('small.jpg', 3 * 1024 * 1024, 'image/jpeg'); // 3MB
      const chunkSize = 5 * 1024 * 1024; // 5MB
      
      const chunks = splitFileIntoChunks(file, chunkSize);
      
      expect(chunks).toHaveLength(1);
      expect(chunks[0].size).toBe(3 * 1024 * 1024);
    });

    test('should handle files exactly equal to chunk size', () => {
      const file = new MockFile('exact.mp4', 5 * 1024 * 1024, 'video/mp4'); // 5MB
      const chunkSize = 5 * 1024 * 1024; // 5MB
      
      const chunks = splitFileIntoChunks(file, chunkSize);
      
      expect(chunks).toHaveLength(1);
      expect(chunks[0].size).toBe(5 * 1024 * 1024);
    });

    test('should use default chunk size of 5MB', () => {
      const file = new MockFile('test.mp4', 12 * 1024 * 1024, 'video/mp4'); // 12MB
      
      const chunks = splitFileIntoChunks(file); // No chunk size specified
      
      expect(chunks).toHaveLength(3); // 12MB / 5MB = 2.4, so 3 chunks
    });
  });
});

// Console log test results for manual verification
console.log('=== Upload Helpers Test Results ===');

// Test file size threshold logic
const testFiles = [
  { name: 'small.jpg', size: 3 * 1024 * 1024, type: 'image/jpeg' },
  { name: 'medium.jpg', size: 5 * 1024 * 1024, type: 'image/jpeg' },
  { name: 'large.jpg', size: 7 * 1024 * 1024, type: 'image/jpeg' },
  { name: 'small.mp4', size: 4 * 1024 * 1024, type: 'video/mp4' },
  { name: 'large.mp4', size: 15 * 1024 * 1024, type: 'video/mp4' },
];

testFiles.forEach(fileData => {
  const file = new MockFile(fileData.name, fileData.size, fileData.type);
  const useMultipart = shouldUseMultipartUpload(file);
  const sizeInMB = (fileData.size / (1024 * 1024)).toFixed(1);
  
  console.log(`${fileData.name} (${sizeInMB}MB): ${useMultipart ? 'Multipart' : 'Direct'} upload`);
});

console.log('=== Chunk Split Test ===');
const largeFile = new MockFile('large.mp4', 12 * 1024 * 1024, 'video/mp4');
const chunks = splitFileIntoChunks(largeFile);
console.log(`12MB file split into ${chunks.length} chunks:`);
chunks.forEach((chunk, index) => {
  const chunkSizeInMB = (chunk.size / (1024 * 1024)).toFixed(1);
  console.log(`  Chunk ${index + 1}: ${chunkSizeInMB}MB`);
});

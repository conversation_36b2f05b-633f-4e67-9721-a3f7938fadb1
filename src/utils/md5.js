import CryptoJS from 'crypto-js';

/**
 * Calculate MD5 hash of a file or blob with flexible output format
 * @param {File|Blob} file - File or Blob to calculate hash for
 * @param {Object} options - Configuration options
 * @param {string} options.format - Output format: 'base64' (default) or 'hex'
 * @returns {Promise<string>} MD5 hash in the specified format
 *
 * @example
 * // For AWS pre-signed URL uploads (default)
 * const md5Base64 = await calculateMD5(file);
 *
 * // For hex format if needed
 * const md5Hex = await calculateMD5(file, { format: 'hex' });
 *
 * // Explicit base64 format
 * const md5Base64 = await calculateMD5(file, { format: 'base64' });
 *
 * // For file chunks (Blob objects)
 * const chunkMd5 = await calculateMD5(chunk);
 */
export const calculateMD5 = async (file, options = {}) => {
  const { format = 'base64' } = options;

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const wordArray = CryptoJS.lib.WordArray.create(event.target.result);
        const md5Hash = CryptoJS.MD5(wordArray);

        if (format === 'hex') {
          resolve(md5Hash.toString());
        } else if (format === 'base64') {
          resolve(md5Hash.toString(CryptoJS.enc.Base64));
        } else {
          reject(new Error(`Unsupported format: ${format}. Use 'hex' or 'base64'.`));
        }
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

/**
 * Legacy alias for backward compatibility
 * @deprecated Use calculateMD5 instead
 */
export const calculateFileMD5 = calculateMD5;

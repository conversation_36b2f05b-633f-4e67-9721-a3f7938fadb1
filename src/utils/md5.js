import CryptoJS from 'crypto-js';

/**
 * Calculate MD5 hash of a file or blob with flexible output format
 * @param {File|Blob} file - File or Blob to calculate hash for
 * @param {Object} options - Configuration options
 * @param {string} options.format - Output format: 'base64' (default) or 'hex'
 * @param {number} options.maxSize - Maximum file size in bytes (default: 50MB)
 * @param {number} options.retries - Number of retry attempts (default: 3)
 * @returns {Promise<string>} MD5 hash in the specified format
 *
 * @example
 * // For AWS pre-signed URL uploads (default)
 * const md5Base64 = await calculateMD5(file);
 *
 * // For hex format if needed
 * const md5Hex = await calculateMD5(file, { format: 'hex' });
 *
 * // Explicit base64 format
 * const md5Base64 = await calculateMD5(file, { format: 'base64' });
 *
 * // For file chunks (Blob objects)
 * const chunkMd5 = await calculateMD5(chunk);
 *
 * // With custom limits
 * const md5 = await calculateMD5(file, { maxSize: 10 * 1024 * 1024, retries: 5 });
 */
export const calculateMD5 = async (file, options = {}) => {
  const {
    format = 'base64',
    maxSize = 50 * 1024 * 1024, // 50MB default limit
    retries = 3
  } = options;

  // Input validation
  if (!file || typeof file.size !== 'number') {
    throw new Error('Invalid file or blob object');
  }

  if (file.size > maxSize) {
    throw new Error(`File size (${file.size} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
  }

  if (file.size === 0) {
    throw new Error('Cannot calculate MD5 for empty file');
  }

  // Retry mechanism for MD5 calculation
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await new Promise((resolve, reject) => {
        const reader = new FileReader();

        // Add timeout for large files
        const timeout = setTimeout(() => {
          reader.abort();
          reject(new Error('MD5 calculation timeout'));
        }, 30000); // 30 second timeout

        reader.onload = (event) => {
          clearTimeout(timeout);
          try {
            // Check available memory before processing
            if (performance.memory && performance.memory.usedJSHeapSize > performance.memory.jsHeapSizeLimit * 0.8) {
              throw new Error('Insufficient memory for MD5 calculation');
            }

            const wordArray = CryptoJS.lib.WordArray.create(event.target.result);
            const md5Hash = CryptoJS.MD5(wordArray);

            if (format === 'hex') {
              resolve(md5Hash.toString());
            } else if (format === 'base64') {
              resolve(md5Hash.toString(CryptoJS.enc.Base64));
            } else {
              reject(new Error(`Unsupported format: ${format}. Use 'hex' or 'base64'.`));
            }
          } catch (error) {
            reject(new Error(`MD5 calculation failed: ${error.message}`));
          }
        };

        reader.onerror = (error) => {
          clearTimeout(timeout);
          reject(new Error(`FileReader error: ${error.message || 'Unknown error'}`));
        };

        reader.onabort = () => {
          clearTimeout(timeout);
          reject(new Error('MD5 calculation was aborted'));
        };

        reader.readAsArrayBuffer(file);
      });
    } catch (error) {
      console.warn(`MD5 calculation attempt ${attempt} failed:`, error.message);

      if (attempt === retries) {
        throw new Error(`MD5 calculation failed after ${retries} attempts: ${error.message}`);
      }

      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

/**
 * Legacy alias for backward compatibility
 * @deprecated Use calculateMD5 instead
 */
export const calculateFileMD5 = calculateMD5;

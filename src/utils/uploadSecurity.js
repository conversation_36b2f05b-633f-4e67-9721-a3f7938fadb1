/**
 * Security utilities for file upload operations
 * Provides monitoring, validation, and security checks for upload processes
 */

/**
 * Monitor memory usage during upload operations
 * @returns {Object} Memory usage information
 */
export const getMemoryUsage = () => {
  if (performance.memory) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      usagePercentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100,
      available: performance.memory.jsHeapSizeLimit - performance.memory.usedJSHeapSize,
    };
  }
  return null;
};

/**
 * Check if there's sufficient memory for an operation
 * @param {number} requiredBytes - Estimated bytes needed for operation
 * @param {number} safetyMargin - Safety margin percentage (default: 20%)
 * @returns {boolean} True if sufficient memory is available
 */
export const hasSufficientMemory = (requiredBytes, safetyMargin = 0.2) => {
  const memInfo = getMemoryUsage();
  if (!memInfo) return true; // Assume sufficient if can't measure

  const requiredWithMargin = requiredBytes * (1 + safetyMargin);
  return memInfo.available >= requiredWithMargin;
};

/**
 * Validate file before upload processing
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result
 */
export const validateFileForUpload = (file, options = {}) => {
  const {
    maxSize = 100 * 1024 * 1024, // 100MB default
    allowedTypes = ['image/*', 'video/*'],
    maxNameLength = 255,
  } = options;

  const errors = [];
  const warnings = [];

  // Basic file validation
  if (!file || !(file instanceof File)) {
    errors.push('Invalid file object');
    return { valid: false, errors, warnings };
  }

  // File size validation
  if (file.size === 0) {
    errors.push('File is empty');
  } else if (file.size > maxSize) {
    errors.push(`File size (${formatBytes(file.size)}) exceeds maximum allowed size (${formatBytes(maxSize)})`);
  }

  // File name validation
  if (!file.name || file.name.length === 0) {
    errors.push('File name is required');
  } else if (file.name.length > maxNameLength) {
    errors.push(`File name too long (${file.name.length} > ${maxNameLength} characters)`);
  }

  // File name security check
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    errors.push('File name contains invalid characters');
  }

  // File type validation
  const isAllowedType = allowedTypes.some(allowedType => {
    if (allowedType.endsWith('/*')) {
      const category = allowedType.slice(0, -2);
      return file.type.startsWith(category + '/');
    }
    return file.type === allowedType;
  });

  if (!isAllowedType) {
    errors.push(`File type '${file.type}' is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Memory usage check
  if (!hasSufficientMemory(file.size)) {
    warnings.push('Low memory detected - upload may fail or be slow');
  }

  // Large file warning
  if (file.size > 50 * 1024 * 1024) { // 50MB
    warnings.push('Large file detected - upload may take significant time');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    fileInfo: {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
    }
  };
};

/**
 * Security event logger for upload operations
 */
export class UploadSecurityLogger {
  constructor() {
    this.events = [];
    this.maxEvents = 1000; // Keep last 1000 events
  }

  /**
   * Log a security event
   * @param {string} type - Event type
   * @param {string} message - Event message
   * @param {Object} metadata - Additional metadata
   */
  logEvent(type, message, metadata = {}) {
    const event = {
      timestamp: new Date().toISOString(),
      type,
      message,
      metadata: {
        ...metadata,
        userAgent: navigator.userAgent,
        url: window.location.href,
        memory: getMemoryUsage(),
      }
    };

    this.events.push(event);

    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[UploadSecurity] ${type}: ${message}`, metadata);
    }

    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production' && type === 'error') {
      this.sendToMonitoring(event);
    }
  }

  /**
   * Send security event to monitoring service
   * @param {Object} event - Security event
   */
  async sendToMonitoring(event) {
    try {
      // Replace with your actual monitoring endpoint
      await fetch('/api/security/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      });
    } catch (error) {
      console.error('Failed to send security event to monitoring:', error);
    }
  }

  /**
   * Get recent security events
   * @param {number} limit - Number of events to return
   * @returns {Array} Recent security events
   */
  getRecentEvents(limit = 100) {
    return this.events.slice(-limit);
  }

  /**
   * Get events by type
   * @param {string} type - Event type to filter by
   * @returns {Array} Filtered events
   */
  getEventsByType(type) {
    return this.events.filter(event => event.type === type);
  }
}

/**
 * Format bytes in human-readable format
 * @param {number} bytes - Number of bytes
 * @returns {string} Formatted string
 */
export const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Calculate recommended chunk size based on available memory
 * @param {number} fileSize - Total file size
 * @param {number} defaultChunkSize - Default chunk size
 * @returns {number} Recommended chunk size
 */
export const getRecommendedChunkSize = (fileSize, defaultChunkSize = 5 * 1024 * 1024) => {
  const memInfo = getMemoryUsage();
  if (!memInfo) return defaultChunkSize;

  // Use smaller chunks if memory is limited
  const availableMemory = memInfo.available;
  const memoryBasedChunkSize = Math.floor(availableMemory * 0.1); // Use 10% of available memory

  // Don't go below 1MB or above 10MB
  const minChunkSize = 1 * 1024 * 1024; // 1MB
  const maxChunkSize = 10 * 1024 * 1024; // 10MB

  const recommendedSize = Math.min(
    Math.max(memoryBasedChunkSize, minChunkSize),
    maxChunkSize,
    defaultChunkSize
  );

  return recommendedSize;
};

// Create global security logger instance
export const uploadSecurityLogger = new UploadSecurityLogger();

/**
 * Security-aware chunk processing with monitoring
 * @param {Array} chunks - Array of file chunks
 * @param {Function} processor - Function to process each chunk
 * @param {Object} options - Processing options
 * @returns {Promise<Array>} Processing results
 */
export const secureChunkProcessing = async (chunks, processor, options = {}) => {
  const { maxConcurrent = 1, onProgress = () => { } } = options;
  const results = [];
  const startTime = Date.now();

  uploadSecurityLogger.logEvent('info', 'Starting secure chunk processing', {
    totalChunks: chunks.length,
    maxConcurrent,
  });

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const chunkStartTime = Date.now();

    try {
      // Check memory before processing each chunk
      const memInfo = getMemoryUsage();
      if (memInfo && memInfo.usagePercentage > 90) {
        uploadSecurityLogger.logEvent('warning', 'High memory usage detected during chunk processing', {
          chunkIndex: i,
          memoryUsage: memInfo.usagePercentage,
        });
      }

      const result = await processor(chunk, i);
      results.push(result);

      const chunkDuration = Date.now() - chunkStartTime;
      onProgress((i + 1) / chunks.length * 100);

      // Log slow chunk processing
      if (chunkDuration > 30000) { // 30 seconds
        uploadSecurityLogger.logEvent('warning', 'Slow chunk processing detected', {
          chunkIndex: i,
          duration: chunkDuration,
          chunkSize: chunk.size,
        });
      }

    } catch (error) {
      uploadSecurityLogger.logEvent('error', 'Chunk processing failed', {
        chunkIndex: i,
        error: error.message,
        chunkSize: chunk.size,
      });
      throw error;
    }
  }

  const totalDuration = Date.now() - startTime;
  uploadSecurityLogger.logEvent('info', 'Chunk processing completed', {
    totalChunks: chunks.length,
    totalDuration,
    averageChunkTime: totalDuration / chunks.length,
  });

  return results;
};

# Multipart Upload Implementation

## Overview

This document describes the implementation of multipart upload functionality for files larger than 5MB in the snap-link project. The implementation automatically chooses between direct upload and multipart upload based on file size, providing optimal performance for both small and large files.

## Key Features

- **Automatic Upload Method Selection**: Files ≤ 5MB use direct upload, files > 5MB use multipart upload
- **File Type Agnostic**: Both images and videos follow the same size-based logic
- **Progress Tracking**: Real-time upload progress for both upload methods
- **Error Handling**: Comprehensive error handling for failed uploads
- **MD5 Verification**: Each chunk is verified with MD5 hash for data integrity
- **AWS Header Compliance**: Proper header matching for AWS S3 pre-signed URL uploads

## Implementation Details

### File Size Threshold

```javascript
// 5MB threshold for multipart upload
const MULTIPART_THRESHOLD = 5 * 1024 * 1024; // 5MB

// Usage
if (shouldUseMultipartUpload(file)) {
  // Use multipart upload for files > 5MB
} else {
  // Use direct upload for files ≤ 5MB
}
```

### API Workflow

#### Multipart Upload Process (Files > 5MB)

1. **Initiate Multipart Upload** (`/multipart/upload`)

   ```json
   {
     "filename": "large-video.mp4",
     "content_type": "video/mp4",
     "content_size": "15728640",
     "resource_type": "Public"
   }
   ```

2. **Get Part Upload URLs** (`/upload/part/url`)

   ```json
   {
     "resource_id": "resource_123",
     "content_md5": "base64_encoded_md5",
     "part_number": 1,
     "content_length": "5242880",
     "object_key": "uploads/resource_123",
     "upload_id": "upload_456"
   }
   ```

3. **Upload Each Chunk** (Direct to AWS S3)

   - Split file into 5MB chunks
   - Calculate MD5 for each chunk
   - Upload chunks sequentially with required headers:
     - `Content-Type`: File MIME type
     - `Content-MD5`: Base64 encoded MD5 hash of chunk
     - `Content-Length`: Size of chunk in bytes
   - Track upload progress for each chunk

4. **Complete Upload** (`/complete/upload`)

   ```json
   {
     "resource_id": "resource_123"
   }
   ```

5. **Add to Collection** (`/collection/resource/add`)
   ```json
   {
     "collection_id": "collection_789",
     "resource_id": "resource_123"
   }
   ```

#### Direct Upload Process (Files ≤ 5MB)

1. **Get Upload URL** (`/upload/resource/url`)
2. **Upload File** (Direct to AWS S3)
3. **Complete Upload** (`/complete/upload`)
4. **Add to Collection** (`/collection/resource/add`)

### Code Structure

#### Modified Files

1. **`src/utils/uploadHelpers.js`**

   - Fixed `multipartUpload` function with correct API parameters
   - Updated `shouldUseMultipartUpload` to use 5MB threshold
   - Enhanced MD5 calculation for chunks

2. **`src/hooks/useCollections.js`**

   - Modified `uploadFileToCollection` to use size-based logic
   - Imported `shouldUseMultipartUpload` function

3. **`src/utils/md5.js`**
   - Updated documentation to support Blob objects (chunks)

#### Key Functions

```javascript
// Determine upload method based on file size
export const shouldUseMultipartUpload = (file, threshold = 5 * 1024 * 1024) => {
  return file.size > threshold;
};

// Split file into chunks for multipart upload
export const splitFileIntoChunks = (file, chunkSize = 5 * 1024 * 1024) => {
  const chunks = [];
  let start = 0;
  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size);
    chunks.push(file.slice(start, end));
    start = end;
  }
  return chunks;
};

// Upload chunk with AWS-compliant headers
export const uploadChunk = async (url, chunk, contentType, contentMd5) => {
  const response = await fetch(url, {
    method: 'PUT',
    body: chunk,
    headers: {
      'Content-Type': contentType,
      'Content-MD5': contentMd5,
      'Content-Length': chunk.size.toString(),
    },
  });
  // ... error handling and ETag extraction
};

// Upload file with automatic method selection
const uploadFileToCollection = async (file, collectionId, metadata, onProgress) => {
  if (shouldUseMultipartUpload(file)) {
    return await multipartUpload({ file, ... });
  } else {
    return await directUpload({ file, ... });
  }
};
```

#### AWS Header Requirements

For proper AWS S3 pre-signed URL uploads, the following headers must match exactly between URL generation and upload request:

- **Content-Type**: MIME type of the file/chunk
- **Content-MD5**: Base64 encoded MD5 hash of the file/chunk data
- **Content-Length**: Size of the file/chunk in bytes

This ensures:

1. **Data Integrity**: MD5 verification prevents corrupted uploads
2. **Size Validation**: Content-Length ensures proper chunk size validation
3. **AWS Compliance**: Headers match pre-signed URL requirements

## Benefits

1. **Performance Optimization**: Small files upload faster with direct upload, large files are more reliable with multipart upload
2. **Automatic Selection**: No manual configuration needed - the system automatically chooses the best method
3. **Progress Tracking**: Users see real-time progress for both upload methods
4. **Error Recovery**: Multipart uploads can be resumed if individual chunks fail
5. **Data Integrity**: MD5 verification ensures uploaded data matches original file

## Testing

The implementation includes comprehensive tests in `src/utils/__tests__/uploadHelpers.test.js`:

- File size threshold logic
- Chunk splitting functionality
- Edge cases (files exactly at threshold)
- Custom threshold support

## Usage Examples

```javascript
// Example 1: Small image (3MB) - uses direct upload
const smallImage = new File(['...'], 'photo.jpg', { type: 'image/jpeg' });
// Automatically uses direct upload

// Example 2: Large video (15MB) - uses multipart upload
const largeVideo = new File(['...'], 'video.mp4', { type: 'video/mp4' });
// Automatically uses multipart upload with 3 chunks (5MB + 5MB + 5MB)

// Example 3: Medium image (7MB) - uses multipart upload
const mediumImage = new File(['...'], 'large-photo.jpg', { type: 'image/jpeg' });
// Automatically uses multipart upload with 2 chunks (5MB + 2MB)
```

## Error Handling

The implementation includes robust error handling:

- **Network Errors**: Retry logic for failed chunk uploads
- **API Errors**: Proper error messages and logging
- **Validation Errors**: File type and size validation
- **Progress Errors**: Graceful handling of progress tracking failures

## Future Enhancements

Potential improvements for future versions:

1. **Parallel Chunk Upload**: Upload multiple chunks simultaneously
2. **Resume Capability**: Resume interrupted multipart uploads
3. **Adaptive Chunk Size**: Adjust chunk size based on network conditions
4. **Compression**: Automatic compression for large files
5. **Background Upload**: Continue uploads when user navigates away
